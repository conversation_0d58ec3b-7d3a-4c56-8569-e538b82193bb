<script lang="ts" setup>
import { ref, nextTick } from "vue"
import * as fabric from 'fabric'
import { ElMessage } from "element-plus"
import CanvasToolbar from './components/CanvasToolbar.vue'
import FabricCanvas from './components/FabricCanvas.vue'
import ImagePanel from './components/ImagePanel.vue'

defineOptions({
  name: "UltrasoundCanvas"
})

// 定义事件
const emit = defineEmits<{
  analyzeImage: [imageData: any]
}>()

// 组件引用
const fabricCanvasRef = ref<InstanceType<typeof FabricCanvas>>()

// 画布状态
const isLoading = ref(true)
const canvasReady = ref(false)
const showGrid = ref(true)
const zoomLevel = ref(1)

// 工具栏状态
const toolbarVisible = ref(true)

// 图像列表状态
const imageList = ref<Array<{
  id: string
  name: string
  size: string
  format: string
  uploadTime: string
  thumbnail: string
  fabricObject?: any
}>>([])
const selectedImageId = ref<string>('')

// 画布就绪处理
const handleCanvasReady = (canvas: fabric.Canvas) => {
  canvasReady.value = true
  isLoading.value = false
  ElMessage.success('画布加载完成')
}

// 缩放变化处理
const handleZoomChanged = (zoom: number) => {
  zoomLevel.value = zoom
}

// 缩放控制
const zoomIn = () => {
  fabricCanvasRef.value?.zoomIn()
}

const zoomOut = () => {
  fabricCanvasRef.value?.zoomOut()
}

const resetZoom = () => {
  fabricCanvasRef.value?.resetZoom()
}

const fitToCanvas = () => {
  fabricCanvasRef.value?.fitToCanvas()
}

// 切换网格显示
const toggleGrid = () => {
  showGrid.value = !showGrid.value
}

// 生成缩略图
const generateThumbnail = (imgElement: HTMLImageElement, maxSize = 100): string => {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')!

  const { width, height } = imgElement
  const scale = Math.min(maxSize / width, maxSize / height)

  canvas.width = width * scale
  canvas.height = height * scale

  ctx.drawImage(imgElement, 0, 0, canvas.width, canvas.height)
  return canvas.toDataURL()
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 加载图像
const loadImage = (file: File) => {
  const fabricCanvas = fabricCanvasRef.value?.fabricCanvas()
  if (!fabricCanvas) {
    ElMessage.error('画布未初始化')
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    const imgUrl = e.target?.result as string
    const img = new Image()
    img.onload = () => {
      const fabricImg = new (fabric as any).Image(img, {
        left: fabricCanvas.getWidth() / 2,
        top: fabricCanvas.getHeight() / 2,
        originX: 'center',
        originY: 'center'
      })

      // 如果图像太大，缩放到合适大小
      const maxSize = Math.min(fabricCanvas.getWidth(), fabricCanvas.getHeight()) * 0.95
      if (fabricImg.width! > maxSize || fabricImg.height! > maxSize) {
        const scale = maxSize / Math.max(fabricImg.width!, fabricImg.height!)
        fabricImg.scale(scale)
      }

      // 生成唯一ID
      const imageId = `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      fabricImg.set('imageId', imageId)

      fabricCanvasRef.value?.addImage(fabricImg)

      // 添加到图像列表
      const imageInfo = {
        id: imageId,
        name: file.name,
        size: formatFileSize(file.size),
        format: file.type.split('/')[1].toUpperCase(),
        uploadTime: new Date().toLocaleString('zh-CN'),
        thumbnail: generateThumbnail(img),
        fabricObject: fabricImg
      }

      imageList.value.push(imageInfo)
      selectedImageId.value = imageId

      ElMessage.success('图像加载成功')
    }
    img.onerror = () => {
      ElMessage.error('图像加载失败')
    }
    img.src = imgUrl
  }
  reader.readAsDataURL(file)
}

// 清空画布
const clearCanvas = () => {
  fabricCanvasRef.value?.clearCanvas()
  imageList.value = []
  selectedImageId.value = ''
  ElMessage.success('画布已清空')
}

// 选择图像
const selectImage = (imageId: string) => {
  selectedImageId.value = imageId
  const imageInfo = imageList.value.find(img => img.id === imageId)
  if (imageInfo && imageInfo.fabricObject) {
    fabricCanvasRef.value?.setActiveObject(imageInfo.fabricObject)
  }
}

// 删除图像
const deleteImage = (imageId: string) => {
  console.log('开始删除图像:', imageId)

  const imageInfo = imageList.value.find(img => img.id === imageId)
  if (!imageInfo) {
    console.warn('未找到要删除的图像信息:', imageId)
    ElMessage.error('未找到要删除的图像')
    return
  }

  // 尝试通过 ID 删除（更可靠的方式）
  let removed = false
  if (fabricCanvasRef.value?.removeImageById) {
    removed = fabricCanvasRef.value.removeImageById(imageId) || false
  }

  // 如果通过 ID 删除失败，尝试通过对象引用删除
  if (!removed && imageInfo.fabricObject) {
    console.log('通过 ID 删除失败，尝试通过对象引用删除')
    fabricCanvasRef.value?.removeImage(imageInfo.fabricObject)
    removed = true
  }

  if (removed) {
    // 从图像列表中移除
    imageList.value = imageList.value.filter(img => img.id !== imageId)

    // 更新选中状态
    if (selectedImageId.value === imageId) {
      selectedImageId.value = imageList.value.length > 0 ? imageList.value[0].id : ''
    }

    ElMessage.success('图像已删除')
    console.log('图像删除成功:', imageId)
  } else {
    ElMessage.error('删除图像失败')
    console.error('删除图像失败:', imageId)
  }
}

// 显示/隐藏图像
const toggleImageVisibility = (imageId: string) => {
  const imageInfo = imageList.value.find(img => img.id === imageId)
  if (imageInfo && imageInfo.fabricObject) {
    const isVisible = imageInfo.fabricObject.visible
    imageInfo.fabricObject.set('visible', !isVisible)
    fabricCanvasRef.value?.fabricCanvas()?.renderAll()
  }
}

// 分析图像
const analyzeImage = (imageId: string) => {
  const imageInfo = imageList.value.find((img: any) => img.id === imageId)
  if (imageInfo) {
    try {
      const imageData = {
        id: imageInfo.id,
        name: imageInfo.name,
        size: imageInfo.size,
        format: imageInfo.format,
        uploadTime: imageInfo.uploadTime,
        thumbnail: imageInfo.thumbnail
      }

      emit('analyzeImage', imageData)
      ElMessage.success('正在切换到图像分析...')
    } catch (error) {
      console.error('分析失败:', error)
      ElMessage.error('分析失败，请重试')
    }
  }
}

// SAM分割
const samSegmentation = () => {
  if (imageList.value.length === 0) {
    ElMessage.warning('请先加载图像')
    return
  }

  if (!selectedImageId.value) {
    ElMessage.warning('请先选择要分割的图像')
    return
  }

  ElMessage.info('正在进行SAM分割...')
  // 这里后续会接入SAM分割接口
  setTimeout(() => {
    ElMessage.success('SAM分割完成！')
  }, 2000)
}


</script>

<template>
  <div class="ultrasound-canvas">
    <!-- 工具栏 -->
    <CanvasToolbar
      v-if="canvasReady"
      :zoom-level="zoomLevel"
      :show-grid="showGrid"
      :visible="toolbarVisible"
      @zoom-in="zoomIn"
      @zoom-out="zoomOut"
      @reset-zoom="resetZoom"
      @fit-canvas="fitToCanvas"
      @toggle-grid="toggleGrid"
      @load-image="loadImage"
      @clear-canvas="clearCanvas"
      @sam-segmentation="samSegmentation"
    />

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 画布容器 -->
      <FabricCanvas
        ref="fabricCanvasRef"
        :show-grid="showGrid"
        :is-loading="isLoading"
        @canvas-ready="handleCanvasReady"
        @zoom-changed="handleZoomChanged"
      />

      <!-- 图像列表面板 -->
      <ImagePanel
        :image-list="imageList"
        :selected-image-id="selectedImageId"
        @select-image="selectImage"
        @delete-image="deleteImage"
        @toggle-visibility="toggleImageVisibility"
        @analyze-image="analyzeImage"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.ultrasound-canvas {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
  border-radius: 8px;
  overflow: hidden;
  position: relative;

  .main-content {
    flex: 1;
    display: flex;
    // gap: 16px;
    overflow: hidden;
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .ultrasound-canvas {
    .main-content {
      flex-direction: column;
      gap: 12px;
    }
  }
}
</style>
