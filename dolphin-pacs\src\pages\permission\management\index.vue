<script setup lang="ts">
import type { VxeGridInstance, VxeGridProps } from "vxe-table"
import type {
  PermissionItem,
  PermissionSearchParams
} from "@/common/apis/permission/type"
import { ElMessage, ElMessageBox } from "element-plus"
import { onMounted, reactive, ref, useTemplateRef } from "vue"
import { getPermissionSearchApi } from "@/common/apis/permission"

// 表格实例引用
const xGridDom = useTemplateRef<VxeGridInstance>("xGridDom")

// 搜索表单数据
const searchForm = reactive<PermissionSearchParams>({
  name: "",
  status: undefined
})

// 表格数据
const tableData = ref<PermissionItem[]>([])
// 分页信息
const pagination = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100]
})

// 表格配置
const xGridOpt = reactive<VxeGridProps>({
  border: true,
  stripe: true,
  resizable: true,
  showHeaderOverflow: true,
  showOverflow: true,
  keepSource: true,
  id: "PermissionManagement",
  height: 600,
  loading: false,
  data: tableData as any,
  /** 工具栏配置 */
  toolbarConfig: {
    refresh: true,
    custom: true,
    slots: {
      buttons: "toolbar-btns"
    }
  },
  /** 自定义列配置项 */
  customConfig: {
    /** 是否允许列选中  */
    checkMethod: ({ column }: { column: any }) =>
      !["name"].includes(column.field)
  },
  /** 列配置 */
  columns: [
    {
      type: "checkbox",
      width: 50
    },
    {
      type: "seq",
      width: 70,
      title: "序号"
    },
    {
      field: "id",
      title: "资源ID",
      width: 100
    },
    {
      field: "name",
      title: "名称",
      minWidth: 120
    },
    {
      field: "code",
      title: "编码",
      minWidth: 100
    },
    {
      field: "path",
      title: "路径",
      minWidth: 150
    },
    {
      field: "level",
      title: "层级",
      width: 80
    },
    {
      field: "status",
      title: "状态",
      width: 80,
      slots: { default: "status-column" }
    },
    {
      field: "createTime",
      title: "创建时间",
      minWidth: 160
    },
    {
      field: "updateTime",
      title: "更新时间",
      minWidth: 160
    },
    {
      field: "remark",
      title: "备注",
      minWidth: 120,
      showOverflow: "tooltip"
    },
    {
      title: "操作",
      width: 120,
      fixed: "right",
      slots: { default: "action-column" }
    }
  ]
})

// 权限数据加载函数
async function loadPermissionData(resetPage = false) {
  try {
    xGridOpt.loading = true

    // 如果需要重置页码（搜索时）
    if (resetPage) {
      pagination.currentPage = 1
    }

    const params: PermissionSearchParams = {
      name: searchForm.name || undefined,
      status: searchForm.status || undefined,
      pageNumber: pagination.currentPage,
      pageSize: pagination.pageSize
    }

    const response = await getPermissionSearchApi(params)

    // 打印响应数据结构用于调试
    console.log("权限API响应:", response)

    // 检查响应数据结构
    let permissionData: PermissionItem[] = []
    let total = 0

    if (response.data && response.data.records) {
      permissionData = response.data.records
      total = response.data.totalRow || 0
    } else {
      console.warn("权限API响应数据结构异常:", response)
    }

    // 更新表格数据和分页信息
    tableData.value = permissionData
    pagination.total = total

    console.log("权限数据:", permissionData)
    console.log("总数:", total)

    xGridOpt.loading = false
  } catch (error) {
    console.error("获取权限数据失败:", error)
    ElMessage.error("获取权限数据失败，请稍后重试")
    tableData.value = []
    pagination.total = 0
    xGridOpt.loading = false
  }
}

// 查询数据
function handleQuery() {
  loadPermissionData(true) // 搜索时重置到第一页
}

// 重置搜索
function handleReset() {
  Object.assign(searchForm, {
    name: "",
    status: undefined
  })
  loadPermissionData(true) // 重置时回到第一页
}

// 分页大小改变
function handleSizeChange(size: number) {
  pagination.pageSize = size
  pagination.currentPage = 1 // 改变页大小时回到第一页
  loadPermissionData()
}

// 当前页改变
function handleCurrentChange(page: number) {
  pagination.currentPage = page
  loadPermissionData()
}

// 新增权限
function handleAdd() {
  console.log("新增权限")
  // TODO: 实现新增权限功能
}

// 批量删除
function handleBatchDelete() {
  const checkboxRecords = xGridDom.value?.getCheckboxRecords()
  if (!checkboxRecords || checkboxRecords.length === 0) {
    ElMessage.warning("请选择要删除的数据")
    return
  }

  ElMessageBox.confirm(
    `确定要删除选中的 ${checkboxRecords.length} 条数据吗？`,
    "批量删除",
    {
      type: "warning"
    }
  ).then(() => {
    // TODO: 实现批量删除功能
    console.log("批量删除:", checkboxRecords)
    ElMessage.success("删除成功")
    loadPermissionData()
  })
}

function handleEdit(row: PermissionItem) {
  console.log("编辑资源", row)
  // TODO: 实现编辑功能
}

async function handleDelete(row: PermissionItem) {
  ElMessageBox.confirm("确定要删除该资源吗？", "提示", { type: "warning" })
    .then(async () => {
      // TODO: 替换为实际删除接口
      // await deletePermissionApi(row.id)
      ElMessage.success("删除成功")
      loadPermissionData()
    })
}

onMounted(() => {
  loadPermissionData()
})
</script>

<template>
  <div class="p-4">
    <!-- 搜索区域，单独盒子 -->
    <div class="search-section">
      <el-card class="search-card">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="名称">
            <el-input
              v-model="searchForm.name"
              placeholder="请输入名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="全部"
              clearable
              style="width: 120px"
            >
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              查询
            </el-button>
            <el-button @click="handleReset">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-card class="table-card">
        <vxe-grid ref="xGridDom" v-bind="xGridOpt">
          <!-- 工具栏按钮 -->
          <template #toolbar-btns>
            <vxe-button status="primary" icon="vxe-icon-add" @click="handleAdd">
              新增权限
            </vxe-button>
            <vxe-button
              status="danger"
              icon="vxe-icon-delete"
              @click="handleBatchDelete"
            >
              批量删除
            </vxe-button>
          </template>

          <template #status-column="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'info'" size="small">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>

          <template #action-column="{ row }">
            <el-button type="primary" text size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" text size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </vxe-grid>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="pagination.pageSizes"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<style scoped>
.p-4 {
  padding: 16px;
}

.search-section {
  margin-bottom: 16px;
}

.search-card {
  border-radius: 8px;
}

.table-section {
  margin-bottom: 16px;
}

.table-card {
  border-radius: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.text-right {
  text-align: right;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>
