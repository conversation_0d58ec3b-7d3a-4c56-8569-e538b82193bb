// https://cn.vuejs.org/guide/built-ins/transition

// fade-transform
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.5s;
}
.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}
.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

// layout-logo-fade
.layout-logo-fade-enter-active,
.layout-logo-fade-leave-active {
  transition: opacity 1.5s;
}
.layout-logo-fade-enter-from,
.layout-logo-fade-leave-to {
  opacity: 0;
}
