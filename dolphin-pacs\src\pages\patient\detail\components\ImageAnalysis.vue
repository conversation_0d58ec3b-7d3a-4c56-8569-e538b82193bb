<script lang="ts" setup>
import { ref, onMounted, watch } from "vue"
import { useRouter } from "vue-router"
import { ElMessage } from "element-plus"
import { ArrowLeft, DataAnalysis } from "@element-plus/icons-vue"

defineOptions({
  name: "ImageAnalysis"
})

// 定义props
const props = defineProps<{
  imageData?: any
  patientId?: string
}>()

// 定义事件
const emit = defineEmits<{
  exitAnalysis: []
}>()

// 路由
const router = useRouter()

// 患者信息
const patientInfo = ref({
  name: '',
  gender: '男',
  age: '',
  responsibleId: '',
  contactNumber: '',
  department: '',
  doctor: '',
  examPart: '双侧宫颈动态组件检查部位',
  bedNumber: ''
})

// 图像信息
const imageData = ref<any>(null)
const imageUrl = ref('')

// 报告内容
const reportContent = ref(`
心脏彩超检查报告

1.心脏形态：
  1.左心房（LA）：心房内径正常，房壁回声均匀，未见明显异常回声团块。
  2.右心房（RA）：房腔大小正常，房壁回声正常，未见异常回声。
  3.左心室（LV）：室壁厚度正常，室壁运动正常，未见室壁运动异常。
  4.右心室（RV）：大小及形态正常，室壁回声正常，未见明显异常改变。

2.心脏功能：
  1.左室功能（AO）：测量左室内径，大小正常（测量值未见明显异常）。
  2.左房功能：房室瓣开放正常，房室瓣关闭正常，未见明显反流。

3.心脏瓣膜：
  1.二尖瓣：瓣膜形态正常，开放及关闭功能正常，未见明显狭窄或关闭不全。
  2.三尖瓣：心房室瓣形态正常，瓣膜开放及关闭正常，未见明显异常。

超声影像学检查：上述检查未见，心脏形态、大小、心室壁运动学检查，
基本一致检查。

彩超检查结果：（彩超检查结果，含有彩色血流及心脏）。

彩超检查结果：各检查结果及心脏功能均为正常。

注意事项：
建议定期复查彩超检查，如有不适症状，请及时就诊。

检查医师：
主治医师或主任医师签字，并注明检查时间（如检查时间、心率等）。

心电图（MRI）：彩超检查结果正常，建议定期复查（如检查时间、心率等）。

注意事项：
建议定期复查彩超检查，如有不适症状，请及时就诊。

检查医师或主任医师签字，并注明检查时间。

检查医师或主任医师签字，并注明检查时间（如检查时间、心率等）。

主治医师或主任医师签字，并注明检查时间（如检查时间、心率等）。
`)

// 报告模板选项
const reportTemplates = ref([
  { value: 'cardiac', label: '心脏超声检查报告模板' },
  { value: 'abdominal', label: '腹部超声检查报告模板' },
  { value: 'thyroid', label: '甲状腺超声检查报告模板' },
  { value: 'obstetric', label: '产科超声检查报告模板' }
])

const selectedTemplate = ref('')

// 性别选项
const genderOptions = ref([
  { value: '男', label: '男' },
  { value: '女', label: '女' }
])

// 返回超声图像页面
const goBack = () => {
  emit('exitAnalysis')
}

// 自动分析
const autoAnalyze = () => {
  ElMessage.info('正在进行AI自动分析...')
  // 这里后续会接入AI分析接口
  setTimeout(() => {
    ElMessage.success('分析完成！')
  }, 2000)
}

// 保存报告
const saveReport = () => {
  ElMessage.success('报告已保存')
}

// 打印报告
const printReport = () => {
  window.print()
}

// 图像加载错误处理
const handleImageError = () => {
  ElMessage.error('图像加载失败')
  imageUrl.value = ''
}

// 监听props变化
watch(() => props.imageData, (newImageData) => {
  if (newImageData) {
    imageData.value = newImageData
    imageUrl.value = newImageData.thumbnail || ''
    console.log('接收到图像数据:', newImageData)
  }
}, { immediate: true })

// 处理模板选择变化
watch(selectedTemplate, (newTemplate) => {
  if (newTemplate && props.patientId) {
    // 跳转到报告样式页面
    router.push({
      name: 'ReportStyle',
      params: {
        patientId: props.patientId
      },
      query: {
        template: newTemplate,
        imageId: imageData.value?.id,
        imageName: imageData.value?.name
      }
    })
  }
})

// 组件挂载
onMounted(() => {
  console.log('图像分析组件已挂载')
})
</script>

<template>
  <div class="image-analysis">
    <!-- 顶部导航栏 -->
    <div class="top-nav">
      <!-- 面包屑导航 -->
      <div class="breadcrumb-section">
        <el-breadcrumb separator=">">
          <el-breadcrumb-item>
            <el-button :icon="ArrowLeft" @click="goBack" type="text" class="back-btn">
              超声图像
            </el-button>
          </el-breadcrumb-item>
          <el-breadcrumb-item class="current">图像分析</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <!-- 操作按钮 -->
      <div class="nav-actions">
        <el-button @click="saveReport" type="primary">保存报告</el-button>
        <el-button @click="printReport">打印</el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧患者信息表单 -->
      <div class="patient-form">
        <div class="form-header">
          <h3>患者信息</h3>
        </div>
        
        <el-form :model="patientInfo" label-width="80px" size="small">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="姓名">
                <el-input v-model="patientInfo.name" placeholder="请输入姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="性别">
                <el-select v-model="patientInfo.gender" placeholder="请选择性别">
                  <el-option
                    v-for="item in genderOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="年龄">
                <el-input v-model="patientInfo.age" placeholder="请输入年龄" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="责任ID">
                <el-input v-model="patientInfo.responsibleId" placeholder="请输入责任ID" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="联系号">
                <el-input v-model="patientInfo.contactNumber" placeholder="请输入联系号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="床号">
                <el-input v-model="patientInfo.bedNumber" placeholder="请输入床号" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="申请科室">
                <el-input v-model="patientInfo.department" placeholder="请输入科室" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="申请医生">
                <el-input v-model="patientInfo.doctor" placeholder="请输入医生" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="检查部位">
            <el-input v-model="patientInfo.examPart" />
          </el-form-item>
        </el-form>
      </div>

      <!-- 右侧图像显示区域 -->
      <div class="image-display">
        <div class="image-header">
          <h3>超声图像</h3>
          <el-button 
            :icon="DataAnalysis" 
            type="primary" 
            @click="autoAnalyze"
            class="analyze-btn"
          >
            自动分析
          </el-button>
        </div>
        
        <div class="image-container">
          <img
            v-if="imageUrl"
            :src="imageUrl"
            :alt="imageData?.name || '超声图像'"
            class="ultrasound-image"
            @error="handleImageError"
          />
          <div v-else class="no-image">
            <p>暂无图像数据</p>
            <p class="hint">请从超声画布页面选择图像进行分析</p>
          </div>
        </div>

        <div class="image-info" v-if="imageData">
          <p><strong>文件名：</strong>{{ imageData.name }}</p>
          <p><strong>格式：</strong>{{ imageData.format }}</p>
          <p><strong>大小：</strong>{{ imageData.size }}</p>
          <p><strong>上传时间：</strong>{{ imageData.uploadTime }}</p>
        </div>
      </div>
    </div>

    <!-- 底部报告区域 -->
    <div class="report-section">
      <div class="report-header">
        <h3>检查报告</h3>
        <el-select
          v-model="selectedTemplate"
          placeholder="选择报告模板"
          style="width: 200px;"
        >
          <el-option
            v-for="template in reportTemplates"
            :key="template.value"
            :label="template.label"
            :value="template.value"
          />
        </el-select>
      </div>
      
      <el-input
        v-model="reportContent"
        type="textarea"
        :rows="15"
        placeholder="请输入检查报告内容..."
        class="report-textarea"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.image-analysis {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
  overflow: hidden;

  .top-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    background: var(--el-bg-color);
    border-bottom: 1px solid var(--el-border-color-light);
    flex-shrink: 0;

    .breadcrumb-section {
      flex: 1;

      .el-breadcrumb {
        line-height: 1.5;

        :deep(.el-breadcrumb__item) {
          .el-breadcrumb__inner {
            font-weight: 500;
            color: var(--el-text-color-regular);
          }

          &:last-child .el-breadcrumb__inner {
            color: var(--el-text-color-primary);
            font-weight: 600;
          }

          .back-btn {
            padding: 0;
            font-weight: 500;
            color: var(--el-color-primary);

            &:hover {
              color: var(--el-color-primary-light-3);
            }
          }
        }

        :deep(.el-breadcrumb__separator) {
          margin: 0 8px;
          color: var(--el-text-color-placeholder);
        }
      }
    }

    .nav-actions {
      display: flex;
      gap: 12px;
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    gap: 24px;
    padding: 24px;
    overflow: hidden;
  }

  .patient-form {
    width: 400px;
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    padding: 20px;
    overflow-y: auto;
    flex-shrink: 0;

    .form-header {
      margin-bottom: 20px;
      padding-bottom: 12px;
      border-bottom: 1px solid var(--el-border-color-lighter);

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }

    :deep(.el-form-item) {
      margin-bottom: 16px;
    }

    :deep(.el-form-item__label) {
      font-weight: 500;
      color: var(--el-text-color-regular);
    }
  }

  .image-display {
    flex: 1;
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .image-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 12px;
      border-bottom: 1px solid var(--el-border-color-lighter);

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }

      .analyze-btn {
        font-size: 14px;
      }
    }

    .image-container {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f8f9fa;
      border-radius: 6px;
      overflow: hidden;
      margin-bottom: 16px;

      .ultrasound-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        border-radius: 4px;
      }

      .no-image {
        text-align: center;
        color: var(--el-text-color-placeholder);
        font-size: 14px;

        .hint {
          margin-top: 8px;
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }

    .image-info {
      background: var(--el-bg-color-page);
      padding: 12px;
      border-radius: 6px;
      font-size: 13px;

      p {
        margin: 4px 0;
        color: var(--el-text-color-regular);

        strong {
          color: var(--el-text-color-primary);
        }
      }
    }
  }

  .report-section {
    background: var(--el-bg-color);
    border-top: 1px solid var(--el-border-color-light);
    padding: 20px 24px;
    flex-shrink: 0;

    .report-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }

    .report-textarea {
      :deep(.el-textarea__inner) {
        font-family: 'Microsoft YaHei', sans-serif;
        line-height: 1.6;
        font-size: 14px;
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 1200px) {
  .image-analysis {
    .main-content {
      flex-direction: column;
      gap: 16px;
    }

    .patient-form {
      width: 100%;
    }

    .image-display {
      height: 400px;
    }
  }
}

@media screen and (max-width: 768px) {
  .image-analysis {
    .top-nav {
      padding: 12px 16px;

      h2 {
        font-size: 16px;
      }

      .nav-actions {
        gap: 8px;

        .el-button {
          padding: 8px 12px;
          font-size: 12px;
        }
      }
    }

    .main-content {
      padding: 16px;
    }

    .patient-form,
    .image-display,
    .report-section {
      padding: 16px;
    }
  }
}
</style>
