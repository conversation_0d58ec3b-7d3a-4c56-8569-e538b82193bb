<script lang="ts" setup>
defineOptions({
  name: "ImageActions"
})

interface ImageData {
  id: string
  name: string
  fabricObject?: any
}

interface Props {
  image: ImageData
}

defineProps<Props>()

const emit = defineEmits<{
  toggleVisibility: []
  analyze: []
  delete: []
}>()
</script>

<template>
  <div class="image-actions">
    <el-button
      size="small"
      :icon="'View'"
      circle
      @click.stop="emit('toggleVisibility')"
      :type="image.fabricObject?.visible !== false ? 'primary' : 'default'"
    />
    <el-button
      size="small"
      :icon="'DataAnalysis'"
      circle
      type="success"
      @click.stop="emit('analyze')"
      title="分析图像"
    />
    <el-button
      size="small"
      :icon="'Delete'"
      circle
      type="danger"
      @click.stop="emit('delete')"
    />
  </div>
</template>

<style lang="scss" scoped>
.image-actions {
  display: flex;
  gap: 2px;
  opacity: 1;
  transition: opacity 0.2s;

  :deep(.el-button) {
    padding: 4px;
    width: 22px;
    height: 22px;
    font-size: 12px;
  }
}

// 这个样式将在父组件中处理
</style>
