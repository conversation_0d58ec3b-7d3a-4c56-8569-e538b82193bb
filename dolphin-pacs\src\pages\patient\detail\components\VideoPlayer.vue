<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from "vue"
// 正确导入视频文件
import videoFile from "@@/assets/video/3099938-hd_1920_1080_30fps.mp4"

defineOptions({
  name: "VideoPlayer"
})

// 视频引用
const videoRef = ref<HTMLVideoElement>()

// 播放状态
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const volume = ref(1)
const isMuted = ref(false)
const isFullscreen = ref(false)

// 视频源 - 使用正确导入的视频文件
const videoSrc = videoFile

// 播放/暂停切换
const togglePlay = () => {
  if (!videoRef.value) return
  
  if (isPlaying.value) {
    videoRef.value.pause()
  } else {
    videoRef.value.play()
  }
}

// 音量控制
const handleVolumeChange = (newVolume: number | number[]) => {
  if (!videoRef.value) return
  const volumeValue = Array.isArray(newVolume) ? newVolume[0] : newVolume
  volume.value = volumeValue
  videoRef.value.volume = volumeValue
}

// 静音切换
const toggleMute = () => {
  if (!videoRef.value) return
  isMuted.value = !isMuted.value
  videoRef.value.muted = isMuted.value
}

// 进度条控制
const handleSeek = (newTime: number | number[]) => {
  if (!videoRef.value) return
  const timeValue = Array.isArray(newTime) ? newTime[0] : newTime
  videoRef.value.currentTime = timeValue
}

// 全屏切换
const toggleFullscreen = () => {
  if (!videoRef.value) return
  
  if (!isFullscreen.value) {
    if (videoRef.value.requestFullscreen) {
      videoRef.value.requestFullscreen()
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen()
    }
  }
}

// 格式化时间显示
const formatTime = (time: number): string => {
  const minutes = Math.floor(time / 60)
  const seconds = Math.floor(time % 60)
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

// 视频事件监听
const handleLoadedMetadata = () => {
  if (videoRef.value) {
    duration.value = videoRef.value.duration
  }
}

const handleTimeUpdate = () => {
  if (videoRef.value) {
    currentTime.value = videoRef.value.currentTime
  }
}

const handlePlay = () => {
  isPlaying.value = true
}

const handlePause = () => {
  isPlaying.value = false
}

const handleVolumeChangeEvent = () => {
  if (videoRef.value) {
    volume.value = videoRef.value.volume
    isMuted.value = videoRef.value.muted
  }
}

const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// 组件挂载时添加事件监听
onMounted(() => {
  if (videoRef.value) {
    videoRef.value.addEventListener('loadedmetadata', handleLoadedMetadata)
    videoRef.value.addEventListener('timeupdate', handleTimeUpdate)
    videoRef.value.addEventListener('play', handlePlay)
    videoRef.value.addEventListener('pause', handlePause)
    videoRef.value.addEventListener('volumechange', handleVolumeChangeEvent)

    // 自动播放视频
    videoRef.value.play().catch((error: Error) => {
      console.warn('自动播放失败，可能需要用户交互:', error)
    })
  }

  document.addEventListener('fullscreenchange', handleFullscreenChange)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  if (videoRef.value) {
    videoRef.value.removeEventListener('loadedmetadata', handleLoadedMetadata)
    videoRef.value.removeEventListener('timeupdate', handleTimeUpdate)
    videoRef.value.removeEventListener('play', handlePlay)
    videoRef.value.removeEventListener('pause', handlePause)
    videoRef.value.removeEventListener('volumechange', handleVolumeChangeEvent)
  }
  
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})
</script>

<template>
  <div class="video-player">
    <!-- 视频容器 -->
    <div class="video-container">
      <video
        ref="videoRef"
        :src="videoSrc"
        class="video-element"
        preload="metadata"
        autoplay
        muted
        loop
        playsinline
        @click="togglePlay"
      >
        您的浏览器不支持视频播放。
      </video>
      
      <!-- 自定义控制栏 -->
      <div class="video-controls">
        <!-- 播放/暂停按钮 -->
        <el-button
          :icon="isPlaying ? 'VideoPause' : 'VideoPlay'"
          circle
          size="small"
          @click="togglePlay"
        />
        
        <!-- 时间显示 -->
        <span class="time-display">
          {{ formatTime(currentTime) }} / {{ formatTime(duration) }}
        </span>
        
        <!-- 进度条 -->
        <el-slider
          v-model="currentTime"
          :max="duration"
          :show-tooltip="false"
          class="progress-slider"
          @change="handleSeek"
        />
        
        <!-- 音量控制 -->
        <div class="volume-control">
          <el-button
            :icon="isMuted ? 'Mute' : 'Microphone'"
            circle
            size="small"
            @click="toggleMute"
          />
          <el-slider
            v-model="volume"
            :max="1"
            :step="0.1"
            :show-tooltip="false"
            class="volume-slider"
            @change="handleVolumeChange"
          />
        </div>
        
        <!-- 全屏按钮 -->
        <el-button
          icon="FullScreen"
          circle
          size="small"
          @click="toggleFullscreen"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.video-player {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #000;
  border-radius: 8px;
  overflow: hidden;

  .video-container {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 400px;
    display: flex;
    flex-direction: column;

    .video-element {
      width: 100%;
      flex: 1;
      object-fit: contain;
      background: #000;
      cursor: pointer;
    }

    .video-controls {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      background: rgba(0, 0, 0, 0.8);
      color: white;

      .time-display {
        font-size: 14px;
        color: #fff;
        white-space: nowrap;
        min-width: 100px;
      }

      .progress-slider {
        flex: 1;
        margin: 0 8px;

        :deep(.el-slider__runway) {
          background-color: rgba(255, 255, 255, 0.3);
        }

        :deep(.el-slider__bar) {
          background-color: var(--el-color-primary);
        }

        :deep(.el-slider__button) {
          border-color: var(--el-color-primary);
        }
      }

      .volume-control {
        display: flex;
        align-items: center;
        gap: 8px;

        .volume-slider {
          width: 80px;

          :deep(.el-slider__runway) {
            background-color: rgba(255, 255, 255, 0.3);
          }

          :deep(.el-slider__bar) {
            background-color: var(--el-color-primary);
          }

          :deep(.el-slider__button) {
            border-color: var(--el-color-primary);
          }
        }
      }

      :deep(.el-button) {
        background: transparent;
        border-color: rgba(255, 255, 255, 0.3);
        color: #fff;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          border-color: var(--el-color-primary);
          color: var(--el-color-primary);
        }
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .video-player {
    .video-container {
      min-height: 300px;

      .video-controls {
        padding: 8px 12px;
        gap: 8px;

        .time-display {
          font-size: 12px;
          min-width: 80px;
        }

        .volume-control {
          .volume-slider {
            width: 60px;
          }
        }
      }
    }
  }
}
</style>
