/** 患者信息接口 */
export interface PatientInfo {
  /** 患者ID */
  id: string
  /** 姓名 */
  name: string
  /** 性别 */
  gender: "男" | "女" | "未知"
  /** 年龄 */
  age: number | string
  /** 检查项目 */
  examType: string
  /** 检查时间 */
  examTime: string
  /** 科室 */
  department: string
  /** 状态 */
  status: "待检查" | "检查中" | "分析中" | "已完成" | "已取消"
  /** 备注信息 */
  info?: string | null
  /** 创建时间 */
  createTime?: string
  /** 更新时间 */
  updateTime?: string
  /** 病案号 */
  caseNumber?: string
  /** 申请医生 */
  doctor?: string
  /** 床号 */
  bedNumber?: string
  /** 检查部位 */
  examPart?: string
  /** vxe-table 自动添加上去的属性 */
  _VXE_ID?: string
}

/** 筛选参数接口 */
export interface PatientFilterParams {
  /** 患者姓名 */
  name?: string
  /** 科室 */
  department?: string
  /** 状态 */
  status?: string
  /** 开始时间 */
  startTime?: string
  /** 结束时间 */
  endTime?: string
  /** 页面大小 */
  size?: number
  /** 当前页码 */
  currentPage?: number
}

/** 科室选项 */
export interface DepartmentOption {
  label: string
  value: string
}

/** 状态选项 */
export interface StatusOption {
  label: string
  value: string
  type: "primary" | "success" | "warning" | "danger" | "info"
}

/** 患者列表响应数据 */
export interface PatientListResponse {
  /** 总数 */
  total: number
  /** 患者列表 */
  list: PatientInfo[]
}

/** API响应数据格式 */
export type PatientResponseData = ApiResponseData<PatientListResponse>
