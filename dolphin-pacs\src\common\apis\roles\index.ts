import type * as Roles from "./type"
import { request } from "@/http/axios"

/**
 * 获取角色列表
 * @param params 搜索参数
 * @returns 角色列表数据
 */
export function getRoleListApi(params?: Roles.RoleSearchParams) {
  return request<Roles.RoleListApiResponse>({
    url: "auth/role/list",
    method: "get",
    params
  })
}

/**
 * 搜索角色列表（分页）
 * @param params 搜索参数
 * @returns 分页角色数据
 */
export function searchRoleListApi(params?: Roles.RoleSearchParams) {
  return request<Roles.RoleSearchApiResponse>({
    url: "auth/role/search",
    method: "get",
    params
  })
}

/**
 * 新增角色
 * @param data 角色数据
 * @returns 新增结果
 */
export function addRoleApi(data: Roles.AddRoleRequestData) {
  return request<Roles.AddRoleApiResponse>({
    url: "auth/role/add",
    method: "post",
    data
  })
}

/**
 * 获取用户当前角色列表
 * @param params 用户角色查询参数
 * @returns 用户角色列表数据
 */
export function getUserRoleListApi(params: Roles.UserRoleSearchParams) {
  return request<Roles.RoleListApiResponse>({
    url: "auth/role/list",
    method: "get",
    params
  })
}



/**
 * 绑定用户角色
 * @param data 用户角色绑定数据
 * @returns 绑定结果
 */
export function bindUserRoleApi(data: Roles.BindUserRoleRequestData) {
  return request<Roles.BindUserRoleApiResponse>({
    url: "auth/role/bind",
    method: "post",
    params: data
  })
}

/**
 * 删除角色
 * @param roleId 角色ID
 * @returns 删除结果
 */
export function deleteRoleApi(roleId: string) {
  return request<Roles.DeleteRoleApiResponse>({
    url: "auth/role",
    method: "delete",
    params: { roleId }
  })
}

/**
 * 批量删除角色
 * @param roleIds 角色ID列表（逗号分隔的字符串）
 * @returns 删除结果
 */
export function batchDeleteRoleApi(roleIds: string) {
  return request<Roles.DeleteRoleApiResponse>({
    url: "auth/role/batch",
    method: "delete",
    params: { roleIds }
  })
}
