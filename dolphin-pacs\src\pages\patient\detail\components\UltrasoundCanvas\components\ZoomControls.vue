<script lang="ts" setup>
defineOptions({
  name: "ZoomControls"
})

interface Props {
  zoomLevel: number
}

defineProps<Props>()

const emit = defineEmits<{
  zoomIn: []
  zoomOut: []
  resetZoom: []
  fitCanvas: []
}>()
</script>

<template>
  <div class="zoom-controls">
    <el-button-group>
      <el-button :icon="'ZoomIn'" size="small" @click="emit('zoomIn')">
        放大
      </el-button>
      <el-button :icon="'ZoomOut'" size="small" @click="emit('zoomOut')">
        缩小
      </el-button>
      <el-button size="small" @click="emit('resetZoom')">
        重置 ({{ Math.round(zoomLevel * 100) }}%)
      </el-button>
      <el-button size="small" @click="emit('fitCanvas')">
        适应
      </el-button>
    </el-button-group>
  </div>
</template>

<style lang="scss" scoped>
.zoom-controls {
  :deep(.el-button-group) {
    .el-button {
      border-radius: 4px;

      &:not(:first-child) {
        margin-left: -1px;
      }

      &:first-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }

      &:last-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }

      &:not(:first-child):not(:last-child) {
        border-radius: 0;
      }
    }
  }
}
</style>
