export interface LoginRequestData {
  /** 用户名 */
  username: string
  /** 密码 */
  password: string
}

export interface LoginTokenData {
  /** Token类型 */
  tokenType: string
  /** 访问令牌 */
  accessToken: string
  /** 刷新令牌 */
  refreshToken: string
  /** 用户名 - 可选，如果后端返回可以使用 */
  username?: string
  /** 用户角色 - 可选，将来需要权限控制时可以使用 */
  roles?: string[]
}

export type LoginResponseData = ApiResponseData<LoginTokenData>
