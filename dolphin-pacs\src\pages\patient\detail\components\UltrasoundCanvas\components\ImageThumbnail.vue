<script lang="ts" setup>
defineOptions({
  name: "ImageThumbnail"
})

interface Props {
  src: string
  alt: string
  size?: 'small' | 'medium' | 'large'
}

withDefaults(defineProps<Props>(), {
  size: 'medium'
})
</script>

<template>
  <div class="image-thumbnail" :class="`size-${size}`">
    <img :src="src" :alt="alt" />
  </div>
</template>

<style lang="scss" scoped>
.image-thumbnail {
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid var(--el-border-color-lighter);
  
  &.size-small {
    width: 32px;
    height: 32px;
  }
  
  &.size-medium {
    width: 40px;
    height: 40px;
  }
  
  &.size-large {
    width: 60px;
    height: 60px;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>
