<script lang="ts" setup>
import { computed } from 'vue'
import PanelHeader from './PanelHeader.vue'
import ImageDetails from './ImageDetails.vue'
import ImageList from './ImageList.vue'

defineOptions({
  name: "ImagePanel"
})

interface ImageData {
  id: string
  name: string
  size: string
  format: string
  uploadTime: string
  thumbnail: string
  fabricObject?: any
}

interface Props {
  imageList: ImageData[]
  selectedImageId: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  selectImage: [id: string]
  deleteImage: [id: string]
  toggleVisibility: [id: string]
  analyzeImage: [id: string]
}>()

// 计算当前选中的图像
const selectedImage = computed(() => {
  return props.imageList.find(img => img.id === props.selectedImageId) || null
})
</script>

<template>
  <div class="image-panel">
    <PanelHeader :image-count="imageList.length" />
    
    <ImageDetails
      v-if="selectedImage"
      :image="selectedImage"
    />
    
    <ImageList
      :images="imageList"
      :selected-id="selectedImageId"
      @select="emit('selectImage', $event)"
      @delete="emit('deleteImage', $event)"
      @toggle-visibility="emit('toggleVisibility', $event)"
      @analyze="emit('analyzeImage', $event)"
    />
  </div>
</template>

<style lang="scss" scoped>
.image-panel {
  width: 300px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// 响应式设计
@media screen and (max-width: 768px) {
  .image-panel {
    width: 100%;
    height: 200px;
  }
}
</style>
