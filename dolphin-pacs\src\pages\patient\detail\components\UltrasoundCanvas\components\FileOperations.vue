<script lang="ts" setup>
import { ElMessage } from "element-plus"

defineOptions({
  name: "FileOperations"
})

const emit = defineEmits<{
  loadImage: [file: File]
  clearCanvas: []
  samSegmentation: []
}>()

const handleFileUpload = (file: File) => {
  emit('loadImage', file)
  return false // 阻止默认上传行为
}

const handleSamSegmentation = () => {
  emit('samSegmentation')
}
</script>

<template>
  <div class="file-operations">
    <el-upload
      :show-file-list="false"
      :before-upload="handleFileUpload"
      accept="image/*"
    >
      <el-button :icon="'Upload'" size="small" type="primary">
        加载图像
      </el-button>
    </el-upload>

    <el-button :icon="'Delete'" size="small" @click="emit('clearCanvas')">
      清空
    </el-button>

    <el-button size="small" type="success" @click="handleSamSegmentation">
      SAM分割
    </el-button>
  </div>
</template>

<style lang="scss" scoped>
.file-operations {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
