/** 声明 vite 环境变量的类型（如果未声明则默认是 any） */
interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_BASE_URL: string
  readonly VITE_ROUTER_HISTORY: "hash" | "html5"
  readonly VITE_PUBLIC_PATH: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

/** 声明静态资源模块类型 */
declare module "*.mp4" {
  const src: string
  export default src
}

declare module "*.webm" {
  const src: string
  export default src
}

declare module "*.ogg" {
  const src: string
  export default src
}
