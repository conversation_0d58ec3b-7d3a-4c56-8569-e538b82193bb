// 统一处理 Cookie

import { <PERSON><PERSON><PERSON><PERSON> } from "@@/constants/cache-key"
import Cookies from "js-cookie"

export function getToken() {
  return Cookies.get(CacheKey.TOKEN)
}

export function setToken(token: string) {
  Cookies.set(CacheKey.TOKEN, token)
}

export function removeToken() {
  Cookies.remove(CacheKey.TOKEN)
}

export function getRefreshToken() {
  return Cookies.get(CacheKey.REFRESH_TOKEN)
}

export function setRefreshToken(refreshToken: string) {
  Cookies.set(CacheKey.REFRESH_TOKEN, refreshToken)
}

export function removeRefreshToken() {
  Cookies.remove(CacheKey.REFRESH_TOKEN)
}
