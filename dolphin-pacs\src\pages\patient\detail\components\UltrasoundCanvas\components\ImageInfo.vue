<script lang="ts" setup>
defineOptions({
  name: "ImageInfo"
})

interface ImageData {
  id: string
  name: string
  size: string
  format: string
  uploadTime: string
  fabricObject?: any
}

interface Props {
  image: ImageData
  showDetails?: boolean
}

withDefaults(defineProps<Props>(), {
  showDetails: false
})
</script>

<template>
  <div class="image-info">
    <div v-if="!showDetails" class="basic-info">
      <div class="item-name">{{ image.name }}</div>
      <div class="item-meta">{{ image.size }}</div>
    </div>
    
    <div v-else class="detail-info">
      <div class="detail-item">
        <label>文件名：</label>
        <span>{{ image.name }}</span>
      </div>
      <div class="detail-item">
        <label>尺寸：</label>
        <span>{{ image.fabricObject?.width || 0 }} × {{ image.fabricObject?.height || 0 }}</span>
      </div>
      <div class="detail-item">
        <label>大小：</label>
        <span>{{ image.size }}</span>
      </div>
      <div class="detail-item">
        <label>格式：</label>
        <span>{{ image.format }}</span>
      </div>
      <div class="detail-item">
        <label>上传时间：</label>
        <span>{{ image.uploadTime }}</span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.image-info {
  flex: 1;
  min-width: 0;

  .basic-info {
    .item-name {
      font-size: 13px;
      color: var(--el-text-color-primary);
      margin-bottom: 2px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .item-meta {
      font-size: 11px;
      color: var(--el-text-color-regular);
    }
  }

  .detail-info {
    .detail-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 13px;

      &:last-child {
        margin-bottom: 0;
      }

      label {
        color: var(--el-text-color-regular);
        font-weight: 500;
        min-width: 60px;
      }

      span {
        color: var(--el-text-color-primary);
        text-align: right;
        word-break: break-all;
      }
    }
  }
}
</style>
