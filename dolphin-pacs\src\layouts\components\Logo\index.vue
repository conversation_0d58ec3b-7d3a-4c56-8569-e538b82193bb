<script lang="ts" setup>
import logo from "@@/assets/images/logo/logo.png?url"
import { useLayoutMode } from "@@/composables/useLayoutMode"

interface Props {
  collapse?: boolean
}

const { collapse = true } = defineProps<Props>()

const { isTop } = useLayoutMode()
</script>

<template>
  <div class="layout-logo-container" :class="{ 'collapse': collapse, 'layout-mode-top': isTop }">
    <transition name="layout-logo-fade">
      <router-link v-if="collapse" key="collapse" to="/">
        <img :src="logo" class="layout-logo">
       
      </router-link>
      <router-link v-else key="expand" to="/" class="layout-logo-expand">
        <img :src="logo" class="layout-logo-icon">
        <span class="layout-logo-title">PACS系统</span>
      </router-link>
    </transition>
  </div>
</template>

<style lang="scss" scoped>
.layout-logo-container {
  position: relative;
  width: 100%;
  height: var(--v3-header-height);
  line-height: var(--v3-header-height);
  text-align: center;
  overflow: hidden;
  .layout-logo {
    display: none;
  }
  .layout-logo-text {
    height: 100%;
    vertical-align: middle;
  }
  .layout-logo-expand {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    height: 100%;
    text-decoration: none;
    color: inherit;
  }
  .layout-logo-icon {
    height: 55px;
    width: 50px;
    vertical-align: middle;
  }
  .layout-logo-title {
    font-size: 25px;
    font-weight: 600;
    color: #267ed6;
    white-space: nowrap;
  }
}

.layout-mode-top {
  height: var(--v3-navigationbar-height);
  line-height: var(--v3-navigationbar-height);
}

.collapse {
  .layout-logo {
    width: 32px;
    height: 32px;
    vertical-align: middle;
    display: inline-block;
  }
  .layout-logo-text {
    display: none;
  }
  .layout-logo-expand {
    display: none;
  }
}
</style>
