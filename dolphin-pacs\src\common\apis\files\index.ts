import type * as Files from "./type"
import { request } from "@/http/axios"

/**
 * 文件上传接口
 * @param file 要上传的文件
 * @param type 上传类型（可选）
 * @param folder 文件夹路径（可选）
 * @returns 上传结果
 */
export function uploadFileApi(
  file: File, 
  type?: 'avatar' | 'document' | 'image',
  folder?: string
): Promise<Files.FileUploadApiResponse> {
  const formData = new FormData()
  formData.append('file', file)
  
  if (type) {
    formData.append('type', type)
  }
  
  if (folder) {
    formData.append('folder', folder)
  }

  return request<Files.FileUploadApiResponse>({
    url: "file/upload",
    method: "post",
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    // 文件上传可能需要更长的超时时间
    timeout: 30000
  })
}

/**
 * 头像上传接口
 * @param file 头像文件
 * @param userId 用户ID
 * @returns 上传结果
 */
export function uploadAvatar<PERSON>pi(file: File, userId: number): Promise<Files.AvatarUploadApiResponse> {
  const formData = new FormData()
  formData.append('avatar', file)
  formData.append('userId', userId.toString())

  return request<Files.AvatarUploadApiResponse>({
    url: "user/avatar/upload",
    method: "post",
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    // 文件上传可能需要更长的超时时间
    timeout: 30000
  })
}

/**
 * 文档上传接口（uploadFileApi的便捷方法）
 * @param file 文档文件
 * @returns 上传结果
 */
export function uploadDocumentApi(file: File): Promise<Files.FileUploadApiResponse> {
  return uploadFileApi(file, 'document', 'documents')
}

/**
 * 图片上传接口（uploadFileApi的便捷方法）
 * @param file 图片文件
 * @returns 上传结果
 */
export function uploadImageApi(file: File): Promise<Files.FileUploadApiResponse> {
  return uploadFileApi(file, 'image', 'images')
}

/**
 * 删除文件接口
 * @param fileId 文件ID或路径
 * @returns 删除结果
 */
export function deleteFileApi(fileId: string): Promise<Files.FileDeleteApiResponse> {
  return request<Files.FileDeleteApiResponse>({
    url: `file/delete/${fileId}`,
    method: "delete"
  })
}

/**
 * 文件上传前的验证工具函数
 * @param file 要验证的文件
 * @param config 文件类型配置
 * @returns 验证结果
 */
export function validateFile(file: File, config: Files.FileTypeConfig): { valid: boolean; message?: string } {
  // 检查文件大小
  const fileSizeMB = file.size / 1024 / 1024
  if (fileSizeMB > config.maxSize) {
    return {
      valid: false,
      message: `文件大小不能超过 ${config.maxSize}MB，当前文件大小：${fileSizeMB.toFixed(2)}MB`
    }
  }

  // 检查文件扩展名
  const fileExtension = file.name.split('.').pop()?.toLowerCase()
  if (!fileExtension || !config.allowedExtensions.includes(fileExtension)) {
    return {
      valid: false,
      message: `不支持的文件类型，支持的格式：${config.allowedExtensions.join(', ')}`
    }
  }

  return { valid: true }
}

/**
 * 默认文件上传配置
 */
export const DEFAULT_FILE_CONFIG: Record<string, Files.FileTypeConfig> = {
  avatar: {
    allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
    maxSize: 2, // 2MB
    description: '头像图片'
  },
  document: {
    allowedExtensions: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
    maxSize: 10, // 10MB
    description: '文档文件'
  },
  image: {
    allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'],
    maxSize: 5, // 5MB
    description: '图片文件'
  }
}
