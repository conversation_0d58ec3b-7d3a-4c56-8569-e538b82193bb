<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from "vue"
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from "element-plus"
import { Star, StarFilled } from "@element-plus/icons-vue"
import { useSettingsStore } from "@/pinia/stores/settings"

defineOptions({
  name: "ReportStyle"
})

const route = useRoute()
const router = useRouter()
const settingsStore = useSettingsStore()

// 保存原始的showFooter状态
const originalShowFooter = ref(settingsStore.showFooter)

// 获取患者ID和模板类型
const patientId = route.params.patientId as string
const templateType = route.query.template as string || 'cardiac'

// 患者信息
const patientInfo = ref({
  name: "张三",
  gender: "男",
  age: "35",
  department: "心内科",
  examNumber: "US20240101001",
  examDate: "2024年01月01日",
  examPart: "心脏超声检查",
  clinicalDiagnosis: "胸闷待查"
})

// 根据模板类型获取报告内容
const getReportContentByTemplate = (template: string) => {
  const templates = {
    cardiac: {
      title: '心脏超声检查报告',
      findings: `心脏彩超检查所见：
1. 心脏形态：心脏大小正常，心房心室比例协调
2. 心脏功能：左室收缩功能正常，射血分数约65%
3. 心脏瓣膜：各瓣膜形态结构正常，启闭功能良好
4. 心包：心包无积液`,
      diagnosis: `超声诊断：
心脏彩超检查未见明显异常`
    },
    abdominal: {
      title: '腹部超声检查报告',
      findings: `腹部超声检查所见：
1. 肝脏：大小形态正常，实质回声均匀
2. 胆囊：大小正常，壁不厚，腔内无异常回声
3. 胰腺：显示清晰，大小形态正常
4. 脾脏：大小形态正常，实质回声均匀
5. 双肾：大小形态正常，皮质回声正常`,
      diagnosis: `超声诊断：
腹部超声检查未见明显异常`
    },
    thyroid: {
      title: '甲状腺超声检查报告',
      findings: `甲状腺超声检查所见：
1. 甲状腺：双侧叶大小正常，形态规整
2. 实质回声：回声均匀，未见明显异常回声团块
3. 血流信号：血流分布正常
4. 淋巴结：颈部未见肿大淋巴结`,
      diagnosis: `超声诊断：
甲状腺超声检查未见明显异常`
    },
    obstetric: {
      title: '产科超声检查报告',
      findings: `产科超声检查所见：
1. 胎儿：单胎头位，胎心率正常
2. 胎儿生物测量：双顶径、头围、腹围、股骨长度均符合孕周
3. 胎盘：位置正常，成熟度正常
4. 羊水：羊水量正常
5. 脐带：脐带绕颈情况正常`,
      diagnosis: `超声诊断：
单胎宫内妊娠，胎儿发育正常`
    }
  }
  return templates[template as keyof typeof templates] || templates.cardiac
}

// 报告内容
const reportContent = ref(getReportContentByTemplate(templateType))

// 评分相关
const satisfactionScore = ref(0)
const diagnosticAccuracy = ref(0)
const feedback = ref("")

// 编辑状态
const isEditing = ref(false)

// 星级评分处理
const handleStarClick = (score: number, type: 'satisfaction' | 'accuracy') => {
  if (type === 'satisfaction') {
    satisfactionScore.value = score
  } else {
    diagnosticAccuracy.value = score
  }
}

// 发送报告
const sendReport = () => {
  ElMessage.success("报告发送成功")
}

// AI辅助报告
const aiAssistReport = () => {
  ElMessage.info("AI辅助报告功能")
}

// 编辑报告
const editReport = () => {
  isEditing.value = !isEditing.value
  if (isEditing.value) {
    ElMessage.success("进入编辑模式")
  } else {
    ElMessage.success("退出编辑模式")
  }
}

// 保存报告
const saveReport = () => {
  ElMessage.success("报告保存成功")
}

// 更改报告
const changeReport = () => {
  ElMessage.info("更改报告功能")
}

// 提交评分
const submitScore = () => {
  if (satisfactionScore.value === 0 || diagnosticAccuracy.value === 0) {
    ElMessage.warning("请完成评分后再提交")
    return
  }
  ElMessage.success("评分提交成功")
}

// 返回上一页
const goBack = () => {
  router.back()
}

onMounted(() => {
  // 隐藏Footer
  settingsStore.showFooter = false

  // 初始化数据
  console.log("报告样式页面加载，患者ID:", patientId)
  console.log("选择的模板类型:", templateType)

  // 根据模板类型更新报告内容
  reportContent.value = getReportContentByTemplate(templateType)
})

// 组件卸载时恢复Footer显示状态
onUnmounted(() => {
  settingsStore.showFooter = originalShowFooter.value
})
</script>

<template>
  <div class="report-style-page">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧报告单区域 -->
      <div class="report-section">
        <!-- 报告标题 -->
        <div class="report-header">
          <div class="hospital-info">
            <h2>XXX 医院{{ reportContent.title }}</h2>
          </div>
        </div>

        <!-- 患者基本信息 -->
        <div class="patient-basic-info">
          <div class="info-row">
            <span class="label">姓名：</span>
            <span v-if="!isEditing" class="value">{{ patientInfo.name }}</span>
            <el-input v-else v-model="patientInfo.name" class="edit-input" size="small" />
            <span class="label">性别：</span>
            <span v-if="!isEditing" class="value">{{ patientInfo.gender }}</span>
            <el-input v-else v-model="patientInfo.gender" class="edit-input" size="small" />
            <span class="label">年龄：</span>
            <span v-if="!isEditing" class="value">{{ patientInfo.age }}</span>
            <el-input v-else v-model="patientInfo.age" class="edit-input" size="small" />
            <span class="label">科室：</span>
            <span v-if="!isEditing" class="value">{{ patientInfo.department }}</span>
            <el-input v-else v-model="patientInfo.department" class="edit-input" size="small" />
          </div>
          <div class="info-row">
            <span class="label">检查号：</span>
            <span v-if="!isEditing" class="value">{{ patientInfo.examNumber }}</span>
            <el-input v-else v-model="patientInfo.examNumber" class="edit-input" size="small" />
            <span class="label">检查日期：</span>
            <span v-if="!isEditing" class="value">{{ patientInfo.examDate }}</span>
            <el-input v-else v-model="patientInfo.examDate" class="edit-input" size="small" />
          </div>
          <div class="info-row">
            <span class="label">检查项目：</span>
            <span v-if="!isEditing" class="value">{{ patientInfo.examPart }}</span>
            <el-input v-else v-model="patientInfo.examPart" class="edit-input" size="small" />
          </div>
        </div>

        <!-- 图片显示区域 -->
        <div class="images-section">
          <div class="image-container">
            <div class="image-placeholder">
              <span>图片 1</span>
            </div>
          </div>
          <div class="image-container">
            <div class="image-placeholder">
              <span>图片 2</span>
            </div>
          </div>
        </div>

        <!-- 超声检查所见 -->
        <div class="findings-section">
          <div class="section-title">超声检查所见：</div>
          <div v-if="!isEditing" class="content-area">
            {{ reportContent.findings }}
          </div>
          <el-input
            v-else
            v-model="reportContent.findings"
            type="textarea"
            :rows="6"
            class="edit-textarea"
          />
        </div>

        <!-- 超声检查提示/超声诊断 -->
        <div class="diagnosis-section">
          <div class="section-title">超声检查提示/超声诊断：</div>
          <div v-if="!isEditing" class="content-area">
            {{ reportContent.diagnosis }}
          </div>
          <el-input
            v-else
            v-model="reportContent.diagnosis"
            type="textarea"
            :rows="4"
            class="edit-textarea"
          />
        </div>

        <!-- 医师签名区域 -->
        <div class="signature-section">
          <div class="signature-row">
            <span class="label">报告医师：</span>
            <span class="signature-line"></span>
            <span class="label">审核医师：</span>
            <span class="signature-line"></span>
            <span class="label">日期：</span>
            <span class="signature-line"></span>
          </div>
        </div>

        <!-- 底部操作按钮 -->
        <div class="bottom-actions">
          <el-button type="primary" @click="editReport">
            {{ isEditing ? '完成编辑' : '编辑报告' }}
          </el-button>
          <el-button type="success" @click="saveReport">保存报告</el-button>
          <el-button type="warning" @click="changeReport">更改报告</el-button>
        </div>
      </div>

      <!-- 右侧功能区域 -->
      <div class="function-section">
        <!-- 大屏显示内容区域 -->
        <div class="fullscreen-content">
          <div class="content-header">
            <h3>大屏显示的内容</h3>
          </div>
          <div class="content-display">
            <!-- 这里可以显示大屏内容 -->
          </div>
        </div>

        <!-- 底部操作和评分区域 -->
        <div class="bottom-section">
          <!-- 操作按钮 -->
          <div class="action-buttons">
            <el-button type="primary" @click="sendReport" class="action-btn">
              发送结果
            </el-button>
            <el-button type="info" @click="aiAssistReport" class="action-btn">
              AI辅助报告
            </el-button>
          </div>

          <!-- 评分系统 -->
          <div class="rating-section">
            <div class="rating-item">
              <div class="rating-title">入院诊断符合分</div>
              <div class="stars">
                <el-icon
                  v-for="i in 5"
                  :key="`accuracy-${i}`"
                  :class="['star', { active: i <= diagnosticAccuracy }]"
                  @click="handleStarClick(i, 'accuracy')"
                >
                  <StarFilled v-if="i <= diagnosticAccuracy" />
                  <Star v-else />
                </el-icon>
              </div>
            </div>

            <div class="feedback-section">
              <div class="feedback-title">评分意见:</div>
              <el-input
                v-model="feedback"
                type="textarea"
                :rows="3"
                placeholder="请输入评分意见（选填）"
                class="feedback-input"
              />
            </div>

            <el-button type="primary" @click="submitScore" class="submit-score-btn">
              提交评分
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.report-style-page {
  width: 100%;
  min-height: 100vh;
  background: var(--el-bg-color-page);
  padding: 20px;

  .main-content {
    display: flex;
    gap: 24px;
    max-width: 1800px;
    margin: 0 auto;
    min-height: calc(100vh - 40px);
  }

  // 左侧报告单区域
  .report-section {
    flex: 1;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-height: 800px;

    .report-header {
      text-align: center;
      margin-bottom: 30px;
      border-bottom: 2px solid #333;
      padding-bottom: 15px;

      h2 {
        margin: 0;
        font-size: 24px;
        font-weight: bold;
        color: #333;
      }
    }

    .patient-basic-info {
      margin-bottom: 30px;
      font-size: 14px;
      line-height: 1.8;

      .info-row {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        border-bottom: 1px solid #eee;
        padding-bottom: 5px;

        .label {
          font-weight: 500;
          color: #333;
          margin-right: 8px;
          min-width: 60px;
        }

        .value {
          margin-right: 30px;
          color: #666;
          border-bottom: 1px solid #ccc;
          min-width: 80px;
          padding-bottom: 2px;
        }

        .edit-input {
          margin-right: 20px;
          width: 100px;
        }
      }
    }

    .images-section {
      display: flex;
      gap: 20px;
      margin-bottom: 30px;

      .image-container {
        flex: 1;
        height: 200px;
        border: 2px solid #333;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;

        .image-placeholder {
          color: #666;
          font-size: 16px;
          font-weight: 500;
        }
      }
    }

    .findings-section,
    .diagnosis-section {
      margin-bottom: 25px;

      .section-title {
        font-weight: bold;
        font-size: 14px;
        color: #333;
        margin-bottom: 10px;
        border-bottom: 1px solid #ddd;
        padding-bottom: 5px;
      }

      .content-area {
        min-height: 120px;
        border: 1px solid #ddd;
        padding: 15px;
        background: #fafafa;
        border-radius: 4px;
        font-size: 14px;
        line-height: 1.6;
        color: #333;
        white-space: pre-line;
      }

      .edit-textarea {
        width: 100%;

        :deep(.el-textarea__inner) {
          min-height: 120px;
          border: 1px solid #ddd;
          border-radius: 4px;
          padding: 15px;
          font-size: 14px;
          line-height: 1.6;
          background: #fff;
        }
      }
    }

    .signature-section {
      margin-bottom: 30px;
      padding: 20px 0;
      border-top: 1px solid #ddd;

      .signature-row {
        display: flex;
        align-items: center;
        gap: 15px;
        font-size: 14px;

        .label {
          font-weight: 500;
          color: #333;
        }

        .signature-line {
          border-bottom: 1px solid #333;
          min-width: 100px;
          height: 20px;
        }
      }
    }

    .bottom-actions {
      display: flex;
      justify-content: center;
      gap: 15px;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }
  }

  // 右侧功能区域
  .function-section {
    width: 500px;
    flex-shrink: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    height: fit-content;

    .fullscreen-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 400px;

      .content-header {
        padding: 20px 20px 0;
        border-bottom: 1px solid #eee;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 500;
          color: #333;
          padding-bottom: 15px;
        }
      }

      .content-display {
        flex: 1;
        padding: 20px;
        background: #fafafa;
        min-height: 300px;
        border-radius: 0 0 8px 8px;
      }
    }

    .bottom-section {
      padding: 20px;
      border-top: 1px solid #eee;

      .action-buttons {
        display: flex;
        gap: 15px;
        margin-bottom: 25px;

        .action-btn {
          flex: 1;
          height: 40px;
          font-size: 14px;
        }
      }

      .rating-section {
        .rating-item {
          margin-bottom: 20px;

          .rating-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
          }

          .stars {
            display: flex;
            gap: 5px;

            .star {
              font-size: 20px;
              color: #ddd;
              cursor: pointer;
              transition: color 0.2s;

              &.active {
                color: #ffd700;
              }

              &:hover {
                color: #ffd700;
              }
            }
          }
        }

        .feedback-section {
          margin-bottom: 20px;

          .feedback-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
          }

          .feedback-input {
            width: 100%;
          }
        }

        .submit-score-btn {
          width: 100%;
          height: 40px;
          font-size: 14px;
        }
      }
    }
  }

  // 响应式设计
  @media screen and (max-width: 1400px) {
    .main-content {
      flex-direction: column;
      gap: 20px;
    }

    .function-section {
      width: 100%;
    }
  }

  @media screen and (max-width: 768px) {
    padding: 10px;

    .main-content {
      gap: 15px;
    }

    .report-section {
      padding: 20px;
    }

    .function-section {
      .fullscreen-content {
        min-height: 300px;

        .content-display {
          min-height: 200px;
        }
      }

      .bottom-section {
        .action-buttons {
          flex-direction: column;
          gap: 10px;

          .action-btn {
            width: 100%;
          }
        }
      }
    }

    .images-section {
      flex-direction: column;
      gap: 15px;

      .image-container {
        height: 150px;
      }
    }
  }
}</style>
