/** 文件上传请求参数 */
export interface FileUploadRequestData {
  /** 文件对象 */
  file: File
  /** 上传类型（可选） */
  type?: 'avatar' | 'document' | 'image'
  /** 文件夹路径（可选） */
  folder?: string
}

/** 文件上传响应数据 */
export interface FileUploadResponseData {
  /** 文件ID */
  id: string
  /** 文件名 */
  filename: string
  /** 原始文件名 */
  originalName: string
  /** 文件路径 */
  path: string
  /** 文件大小（字节） */
  size: number
  /** 文件类型 */
  mimeType: string
  /** 上传时间 */
  uploadTime: string
  /** 文件URL */
  url?: string
}

/** 文件上传API响应 */
export type FileUploadApiResponse = ApiResponseData<FileUploadResponseData>

/** 头像上传API响应 */
export type AvatarUploadApiResponse = ApiResponseData<string>

/** 文件删除请求参数 */
export interface FileDeleteRequestData {
  /** 文件ID或路径 */
  fileId: string
}

/** 文件删除响应数据 */
export interface FileDeleteResponseData {
  /** 是否删除成功 */
  success: boolean
  /** 删除的文件路径 */
  path: string
}

/** 文件删除API响应 */
export type FileDeleteApiResponse = ApiResponseData<FileDeleteResponseData>

/** 支持的文件类型配置 */
export interface FileTypeConfig {
  /** 允许的文件扩展名 */
  allowedExtensions: string[]
  /** 最大文件大小（MB） */
  maxSize: number
  /** 文件类型描述 */
  description: string
}

/** 文件上传配置 */
export interface FileUploadConfig {
  /** 头像文件配置 */
  avatar: FileTypeConfig
  /** 文档文件配置 */
  document: FileTypeConfig
  /** 图片文件配置 */
  image: FileTypeConfig
}
